// 消息状态管理 - Pinia Store
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { TextMessage, wsService, WebSocketState } from '../services/websocketService'
import { apiClient } from '../api'

// 消息接口定义
export interface Message {
  id: string
  senderId: string
  receiverId: string
  content: string
  timestamp: number
  type: number
  isRead?: boolean
  isSending?: boolean
  sendError?: string
}

// 聊天会话接口
export interface ChatSession {
  userId: string
  userName: string
  userAvatar: string
  lastMessage?: Message
  unreadCount: number
  lastActiveTime: number
}

// 聊天历史响应接口
export interface ChatHistoryResponse {
  success: boolean
  messages: Message[]
  pagination: {
    page: number
    limit: number
    total: number
    pages: number
  }
}

export const useMessageStore = defineStore('message', () => {
  // 状态
  const messages = ref<Map<string, Message[]>>(new Map()) // 按用户ID分组的消息
  const chatSessions = ref<Map<string, ChatSession>>(new Map()) // 聊天会话
  const currentChatUserId = ref<string>('') // 当前聊天的用户ID
  const wsState = ref<WebSocketState>(WebSocketState.DISCONNECTED)
  const isLoading = ref(false)
  const error = ref<string>('')

  // 计算属性
  const currentMessages = computed(() => {
    if (!currentChatUserId.value) return []
    return messages.value.get(currentChatUserId.value) || []
  })

  const sortedChatSessions = computed(() => {
    return Array.from(chatSessions.value.values()).sort(
      (a, b) => b.lastActiveTime - a.lastActiveTime
    )
  })

  const totalUnreadCount = computed(() => {
    return Array.from(chatSessions.value.values()).reduce(
      (total, session) => total + session.unreadCount,
      0
    )
  })

  // 初始化WebSocket连接
  const initWebSocket = async (token?: string) => {
    try {
      // 设置WebSocket事件监听
      wsService.on('onStateChange', (state) => {
        wsState.value = state
        console.log('WebSocket状态变更:', state)
      })

      wsService.on('onMessage', (message) => {
        handleIncomingMessage(message)
      })

      wsService.on('onError', (errorMsg) => {
        error.value = errorMsg.message
        console.error('WebSocket错误:', errorMsg)
      })

      wsService.on('onHeartbeat', (heartbeat) => {
        console.log('收到心跳响应:', heartbeat)
      })

      // 连接WebSocket
      await wsService.connect(token)
      console.log('WebSocket连接成功')
    } catch (err) {
      console.error('WebSocket连接失败:', err)
      error.value = err instanceof Error ? err.message : '连接失败'
    }
  }

  // 处理接收到的消息
  const handleIncomingMessage = (textMessage: TextMessage) => {
    const message: Message = {
      id: textMessage.id || `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      senderId: textMessage.senderId,
      receiverId: textMessage.receiverId,
      content: textMessage.content,
      timestamp: textMessage.timestamp || Date.now(),
      type: 1, // TEXT_MESSAGE
      isRead: false
    }

    // 添加消息到对应的聊天
    const chatUserId =
      message.senderId === getCurrentUserId() ? message.receiverId : message.senderId
    addMessageToChat(chatUserId, message)

    // 更新聊天会话
    updateChatSession(chatUserId, message)

    console.log('收到新消息:', message)
  }

  // 发送消息
  const sendMessage = async (receiverId: string, content: string): Promise<boolean> => {
    if (!content.trim()) {
      error.value = '消息内容不能为空'
      return false
    }

    if (!wsService.isConnected()) {
      error.value = 'WebSocket未连接'
      return false
    }

    // 创建临时消息对象
    const tempMessage: Message = {
      id: `temp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      senderId: getCurrentUserId(),
      receiverId,
      content: content.trim(),
      timestamp: Date.now(),
      type: 1,
      isSending: true
    }

    // 立即添加到界面显示
    addMessageToChat(receiverId, tempMessage)
    updateChatSession(receiverId, tempMessage)

    try {
      // 发送消息
      const success = wsService.sendTextMessage(receiverId, content.trim())

      if (success) {
        // 更新消息状态为已发送
        updateMessageStatus(receiverId, tempMessage.id, { isSending: false })
        return true
      } else {
        // 发送失败
        updateMessageStatus(receiverId, tempMessage.id, {
          isSending: false,
          sendError: '发送失败'
        })
        error.value = '消息发送失败'
        return false
      }
    } catch (err) {
      console.error('发送消息失败:', err)
      updateMessageStatus(receiverId, tempMessage.id, {
        isSending: false,
        sendError: '发送失败'
      })
      error.value = err instanceof Error ? err.message : '发送失败'
      return false
    }
  }

  // 获取聊天历史
  const loadChatHistory = async (otherUserId: string, page = 1, limit = 50): Promise<boolean> => {
    if (isLoading.value) return false

    isLoading.value = true
    error.value = ''

    try {
      const response = await apiClient.getChatHistory(otherUserId, page, limit)

      if (response.success && response.messages) {
        // 将历史消息添加到store
        const existingMessages = messages.value.get(otherUserId) || []
        const newMessages = response.messages.filter(
          (msg) => !existingMessages.some((existing) => existing.id === msg.id)
        )

        if (newMessages.length > 0) {
          const allMessages = [...newMessages, ...existingMessages].sort(
            (a, b) => a.timestamp - b.timestamp
          )
          messages.value.set(otherUserId, allMessages)

          // 更新聊天会话
          const lastMessage = allMessages[allMessages.length - 1]
          if (lastMessage) {
            updateChatSession(otherUserId, lastMessage)
          }
        }

        console.log(`加载${otherUserId}的聊天历史成功，共${newMessages.length}条新消息`)
        return true
      } else {
        error.value = '获取聊天历史失败'
        return false
      }
    } catch (err) {
      console.error('获取聊天历史失败:', err)
      error.value = err instanceof Error ? err.message : '获取聊天历史失败'
      return false
    } finally {
      isLoading.value = false
    }
  }

  // 添加消息到聊天
  const addMessageToChat = (userId: string, message: Message) => {
    const userMessages = messages.value.get(userId) || []
    userMessages.push(message)
    messages.value.set(userId, userMessages)
  }

  // 更新消息状态
  const updateMessageStatus = (userId: string, messageId: string, updates: Partial<Message>) => {
    const userMessages = messages.value.get(userId)
    if (userMessages) {
      const messageIndex = userMessages.findIndex((msg) => msg.id === messageId)
      if (messageIndex !== -1) {
        Object.assign(userMessages[messageIndex], updates)
      }
    }
  }

  // 更新聊天会话
  const updateChatSession = async (userId: string, lastMessage: Message) => {
    let session = chatSessions.value.get(userId)

    if (!session) {
      // 创建新的聊天会话，需要获取用户信息
      try {
        const userDetail = await apiClient.getUserDetail(userId)
        if (userDetail.success) {
          session = {
            userId,
            userName: userDetail.user.displayName,
            userAvatar: userDetail.user.avatar,
            lastMessage,
            unreadCount: 0,
            lastActiveTime: lastMessage.timestamp
          }
        } else {
          // 如果获取用户信息失败，使用默认信息
          session = {
            userId,
            userName: `用户${userId}`,
            userAvatar: '/avatars/default.png',
            lastMessage,
            unreadCount: 0,
            lastActiveTime: lastMessage.timestamp
          }
        }
      } catch (err) {
        console.error('获取用户信息失败:', err)
        session = {
          userId,
          userName: `用户${userId}`,
          userAvatar: '/avatars/default.png',
          lastMessage,
          unreadCount: 0,
          lastActiveTime: lastMessage.timestamp
        }
      }
    } else {
      // 更新现有会话
      session.lastMessage = lastMessage
      session.lastActiveTime = lastMessage.timestamp
    }

    // 如果不是当前聊天用户且消息不是自己发送的，增加未读计数
    if (userId !== currentChatUserId.value && lastMessage.senderId !== getCurrentUserId()) {
      session.unreadCount++
    }

    chatSessions.value.set(userId, session)
  }

  // 设置当前聊天用户
  const setCurrentChatUser = (userId: string) => {
    currentChatUserId.value = userId

    // 清除该用户的未读计数
    const session = chatSessions.value.get(userId)
    if (session) {
      session.unreadCount = 0
      chatSessions.value.set(userId, session)
    }

    // 标记该用户的所有消息为已读
    const userMessages = messages.value.get(userId)
    if (userMessages) {
      userMessages.forEach((msg) => {
        if (msg.senderId !== getCurrentUserId()) {
          msg.isRead = true
        }
      })
    }
  }

  // 断开WebSocket连接
  const disconnectWebSocket = () => {
    wsService.disconnect()
    wsState.value = WebSocketState.DISCONNECTED
  }

  // 清除错误
  const clearError = () => {
    error.value = ''
  }

  // 获取当前用户ID
  const getCurrentUserId = (): string => {
    const user = apiClient.getCurrentUser()
    return user?.id || ''
  }

  // 重试发送失败的消息
  const retryMessage = async (userId: string, messageId: string): Promise<boolean> => {
    const userMessages = messages.value.get(userId)
    if (!userMessages) return false

    const message = userMessages.find((msg) => msg.id === messageId)
    if (!message || !message.sendError) return false

    // 清除错误状态，设置为发送中
    updateMessageStatus(userId, messageId, {
      sendError: undefined,
      isSending: true
    })

    // 重新发送
    const success = wsService.sendTextMessage(message.receiverId, message.content)

    if (success) {
      updateMessageStatus(userId, messageId, { isSending: false })
      return true
    } else {
      updateMessageStatus(userId, messageId, {
        isSending: false,
        sendError: '重发失败'
      })
      return false
    }
  }

  return {
    // 状态
    messages,
    chatSessions,
    currentChatUserId,
    wsState,
    isLoading,
    error,

    // 计算属性
    currentMessages,
    sortedChatSessions,
    totalUnreadCount,

    // 方法
    initWebSocket,
    sendMessage,
    loadChatHistory,
    setCurrentChatUser,
    disconnectWebSocket,
    clearError,
    retryMessage
  }
})
